// Enhanced Theme Toggle Implementation
console.log('🌙 Theme toggle script loaded');

// Initialize theme immediately (before D<PERSON> loads)
(function() {
    // Check for saved theme preference or default to system preference
    let savedTheme = localStorage.getItem('theme');

    // If no saved preference, check system preference
    if (!savedTheme) {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            savedTheme = 'dark';
        } else {
            savedTheme = 'light';
        }
        // Save the detected preference
        localStorage.setItem('theme', savedTheme);
    }

    console.log('🎨 Initializing theme:', savedTheme);

    // Apply theme immediately
    document.documentElement.setAttribute('data-theme', savedTheme);

    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
        // Add immediate dark styles
        document.documentElement.style.setProperty('--background-color', '#0f0f0f');
        document.documentElement.style.setProperty('--text-color', '#e4e4e7');
        document.documentElement.style.setProperty('--card-bg', '#18181b');
        document.documentElement.style.setProperty('--border-color', '#27272a');
    } else {
        document.body.classList.remove('dark-theme');
        // Reset to light styles
        document.documentElement.style.removeProperty('--background-color');
        document.documentElement.style.removeProperty('--text-color');
        document.documentElement.style.removeProperty('--card-bg');
        document.documentElement.style.removeProperty('--border-color');
    }
})();

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM ready, setting up theme toggle');

    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');

    console.log('🔍 Found elements:', {
        themeToggle: !!themeToggle,
        themeIcon: !!themeIcon,
        toggleId: themeToggle?.id,
        iconId: themeIcon?.id
    });

    if (!themeToggle || !themeIcon) {
        console.error('❌ Theme toggle elements not found!');
        // Try to find them with a different approach
        setTimeout(() => {
            const toggle = document.querySelector('.theme-toggle');
            const icon = document.querySelector('#themeIcon');
            console.log('🔄 Retry search:', { toggle: !!toggle, icon: !!icon });
        }, 1000);
        return;
    }

    // Set initial icon based on current theme
    const currentTheme = localStorage.getItem('theme') || 'light';
    updateIcon(currentTheme);

    // Add click handler with multiple event types
    const handleThemeToggle = function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('🖱️ Theme toggle clicked!');

        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        console.log('🔄 Switching from', currentTheme, 'to', newTheme);

        // Apply new theme
        applyTheme(newTheme);

        // Save to localStorage
        localStorage.setItem('theme', newTheme);

        // Track manual theme change to prevent auto-switching for a while
        localStorage.setItem('lastManualThemeChange', Date.now().toString());

        // Update icon
        updateIcon(newTheme);

        // Visual feedback
        themeToggle.style.transform = 'scale(0.9)';
        setTimeout(() => {
            themeToggle.style.transform = '';
        }, 150);

        console.log('✅ Theme switched to:', newTheme);
    };

    // Add multiple event listeners for better compatibility
    themeToggle.addEventListener('click', handleThemeToggle);
    themeToggle.addEventListener('touchstart', handleThemeToggle);

    // Add keyboard support
    themeToggle.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            handleThemeToggle(e);
        }
    });

    console.log('✅ Theme toggle setup complete');

    // Listen for system theme changes
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', function(e) {
            // Only auto-switch if user hasn't manually set a preference recently
            const lastManualChange = localStorage.getItem('lastManualThemeChange');
            const now = Date.now();
            const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

            if (!lastManualChange || (now - parseInt(lastManualChange)) > oneHour) {
                const systemTheme = e.matches ? 'dark' : 'light';
                console.log('🔄 System theme changed to:', systemTheme);
                applyTheme(systemTheme);
                localStorage.setItem('theme', systemTheme);
                updateIcon(systemTheme);
            }
        });
    }
});

function applyTheme(theme) {
    console.log('🎨 Applying theme:', theme);

    // Set data attribute
    document.documentElement.setAttribute('data-theme', theme);

    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
        // Force dark theme variables with enhanced colors
        document.documentElement.style.setProperty('--background-color', '#0f0f0f');
        document.documentElement.style.setProperty('--text-color', '#e4e4e7');
        document.documentElement.style.setProperty('--text-light', '#a1a1aa');
        document.documentElement.style.setProperty('--border-color', '#27272a');
        document.documentElement.style.setProperty('--card-bg', '#18181b');
        document.documentElement.style.setProperty('--card-border', '#27272a');
        document.documentElement.style.setProperty('--input-bg', '#18181b');
        document.documentElement.style.setProperty('--input-border', '#3f3f46');
        document.documentElement.style.setProperty('--modal-bg', '#18181b');
        document.documentElement.style.setProperty('--modal-overlay', 'rgba(0, 0, 0, 0.8)');
        document.documentElement.style.setProperty('--navbar-bg', 'rgba(24, 24, 27, 0.95)');
        document.documentElement.style.setProperty('--navbar-border', 'rgba(63, 63, 70, 0.3)');
        document.documentElement.style.setProperty('--section-bg', '#09090b');
        document.documentElement.style.setProperty('--footer-bg', '#18181b');
        document.documentElement.style.setProperty('--footer-text', '#e4e4e7');

        // Enhanced dark mode colors for better contrast
        document.documentElement.style.setProperty('--success-color', '#10b981');
        document.documentElement.style.setProperty('--warning-color', '#f59e0b');
        document.documentElement.style.setProperty('--error-color', '#ef4444');
        document.documentElement.style.setProperty('--link-color', '#8b5cf6');
        document.documentElement.style.setProperty('--link-hover-color', '#a78bfa');
    } else {
        document.body.classList.remove('dark-theme');
        // Reset to light theme variables
        document.documentElement.style.setProperty('--background-color', '#FFFFFF');
        document.documentElement.style.setProperty('--text-color', '#333');
        document.documentElement.style.setProperty('--text-light', '#666');
        document.documentElement.style.setProperty('--border-color', '#e1e5e9');
        document.documentElement.style.setProperty('--card-bg', '#ffffff');
        document.documentElement.style.setProperty('--card-border', '#e1e5e9');
        document.documentElement.style.setProperty('--input-bg', '#ffffff');
        document.documentElement.style.setProperty('--input-border', '#e1e5e9');
        document.documentElement.style.setProperty('--modal-bg', '#ffffff');
        document.documentElement.style.setProperty('--modal-overlay', 'rgba(0, 0, 0, 0.5)');
        document.documentElement.style.setProperty('--navbar-bg', 'rgba(255, 255, 255, 0.95)');
        document.documentElement.style.setProperty('--navbar-border', 'rgba(255, 255, 255, 0.3)');
        document.documentElement.style.setProperty('--section-bg', '#f8f9fa');
        document.documentElement.style.setProperty('--footer-bg', '#2c3e50');
        document.documentElement.style.setProperty('--footer-text', '#ecf0f1');

        // Reset enhanced colors to light theme defaults
        document.documentElement.style.setProperty('--success-color', '#27ae60');
        document.documentElement.style.setProperty('--warning-color', '#ffc107');
        document.documentElement.style.setProperty('--error-color', '#e74c3c');
        document.documentElement.style.removeProperty('--link-color');
        document.documentElement.style.removeProperty('--link-hover-color');
    }

    // Smooth transition for theme change
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';

    // Dispatch custom event for theme change
    const themeChangeEvent = new CustomEvent('themeChanged', {
        detail: { theme: theme }
    });
    document.dispatchEvent(themeChangeEvent);

    console.log('✅ Theme applied. Data-theme:', document.documentElement.getAttribute('data-theme'));
    console.log('📝 Body classes:', document.body.className);
}

function updateIcon(theme) {
    const themeIcon = document.getElementById('themeIcon');
    const themeToggle = document.getElementById('themeToggle');

    if (!themeIcon || !themeToggle) {
        console.warn('⚠️ Icon elements not found for update');
        return;
    }

    if (theme === 'dark') {
        themeIcon.className = 'fas fa-sun';
        themeToggle.setAttribute('title', 'Switch to light mode');
        themeToggle.setAttribute('aria-label', 'Switch to light mode');
    } else {
        themeIcon.className = 'fas fa-moon';
        themeToggle.setAttribute('title', 'Switch to dark mode');
        themeToggle.setAttribute('aria-label', 'Switch to dark mode');
    }

    console.log('🔄 Icon updated for theme:', theme);
}

// Add a manual test function
window.testThemeToggle = function() {
    console.log('🧪 Manual theme toggle test');
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    applyTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    updateIcon(newTheme);
};
